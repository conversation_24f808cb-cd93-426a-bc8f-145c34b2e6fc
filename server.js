const express = require('express');
const http = require('http');
const WebSocket = require('ws');
const sqlite3 = require('sqlite3').verbose();
const { v4: uuidv4 } = require('uuid');
const dbPath = 'voting.db';

const app = express();
const server = http.createServer(app);
const wss = new WebSocket.Server({ 
    server,
    path: '/projects/live_vote_tracker'
});

// Create router with base path
const router = express.Router();
app.use('/projects/live_vote_tracker', router);

// Move static and json middleware to router
router.use(express.static('public'));
router.use(express.json());

const db = new sqlite3.Database(dbPath);
const rooms = new Map(); // Store active rooms and their participants

// Add this function after rooms Map declaration
function setupRoomCleanup(roomId, endTime) {
  const timeUntilEnd = new Date(endTime) - new Date();
  if (timeUntilEnd <= 0) return;

  setTimeout(() => {
    const room = rooms.get(roomId);
    if (!room) return;

    // Notify all participants that room is ending
    broadcastToRoom(roomId, {
      type: 'room-ended',
      roomId: roomId
    });

    // Close all websocket connections in the room
    room.participants.forEach(({ ws }) => {
      if (ws.readyState === WebSocket.OPEN) {
        ws.close();
      }
    });

    // Update room status in database
    db.run('UPDATE rooms SET status = ? WHERE room_id = ?', 
      ['ended', roomId]
    );

    // Delete room from memory
    rooms.delete(roomId);
  }, timeUntilEnd);
}

// HTTP endpoints
router.post('/create-room', (req, res) => {
  const { question, options, startTime, endTime } = req.body;
  const roomId = uuidv4().substring(0, 6);  // Lấy 6 ký tự đầu của UUID

  db.run(
    'INSERT INTO rooms (room_id, question, options, start_time, end_time, status) VALUES (?, ?, ?, ?, ?, ?)',
    [roomId, question, JSON.stringify(options), startTime, endTime, 'active'],
    (err) => {
      if (err) {
        res.status(500).json({ error: err.message });
        return;
      }
      rooms.set(roomId, { 
        participants: new Map(),
        votes: new Array(options.length).fill(0),
        options,
        question,
        startTime,
        endTime
      });
      
      // Setup room cleanup at endTime
      setupRoomCleanup(roomId, endTime);
      
      res.json({ roomId });
    }
  );
});

// Add this API endpoint before WebSocket connection handling
router.get('/room-results/:roomId', (req, res) => {
  const { roomId } = req.params;
  
  db.all(
    `SELECT v.option_index, COUNT(*) as count
     FROM rooms r
     LEFT JOIN votes v ON r.room_id = v.room_id
     WHERE r.room_id = ? AND r.status = 'ended'
     GROUP BY v.option_index`,
    [roomId],
    (err, voteRows) => {
      if (err) {
        res.status(500).json({ error: err.message });
        return;
      }

      // Get room details
      db.get(
        `SELECT question, options
         FROM rooms
         WHERE room_id = ? AND status = 'ended'`,
        [roomId],
        (err, roomRow) => {
          if (err) {
            res.status(500).json({ error: err.message });
            return;
          }
          if (!roomRow) {
            res.status(404).json({ error: 'Room not found or not ended' });
            return;
          }

          const options = JSON.parse(roomRow.options);
          const votes = new Array(options.length).fill(0);
          
          // Fill in the vote counts
          voteRows.forEach(row => {
            if (row.option_index !== null) {
              votes[row.option_index] = row.count;
            }
          });

          res.json({
            question: roomRow.question,
            options,
            votes
          });
        }
      );
    }
  );
});

// WebSocket connection handling
wss.on('connection', (ws) => {
  let userRoom = null;
  let participantKey = null;  // Thêm biến để lưu participantKey
  let userName = null;

  ws.on('message', (message) => {
    const data = JSON.parse(message);

    switch (data.type) {
      case 'join':
        const room = rooms.get(data.roomId);
        if (!room) {
          ws.send(JSON.stringify({ type: 'error', message: 'Room not found' }));
          return;
        }
        userRoom = data.roomId;
        userName = data.userName;
        const uid = uuidv4();
        participantKey = `${uid}:${userName}`; // Lưu participantKey
        room.participants.set(participantKey, { ws, vote: null });
        
        // Send current state to the new participant
        ws.send(JSON.stringify({
          type: 'room-state',
          question: room.question,
          options: room.options,
          votes: room.votes,
          participantCount: room.participants.size,
          participants: Array.from(room.participants.entries()).map(([key, value]) => ({
            name: key.split(':')[1],
            hasVoted: value.vote !== null
          })),
          roomId: userRoom
        }));

        // Notify all participants about new count
        broadcastToRoom(userRoom, {
          type: 'participant-count',
          count: room.participants.size,
          participants: Array.from(room.participants.entries()).map(([key, value]) => ({
            name: key.split(':')[1],
            hasVoted: value.vote !== null
          }))
        });
        break;

      case 'vote':
        if (!userRoom || !participantKey) return; // Kiểm tra bằng participantKey
        const roomData = rooms.get(userRoom);
        if (!roomData) return;

        // Sử dụng participantKey trực tiếp
        const participant = roomData.participants.get(participantKey);
        if (participant.vote !== null) {
          roomData.votes[participant.vote]--;
        }

        // Add new vote
        participant.vote = data.optionIndex;
        roomData.votes[data.optionIndex]++;
        roomData.participants.set(participantKey, participant);

        // Save vote to database với participantKey
        db.run(
          'INSERT OR REPLACE INTO votes (room_id, user_name, option_index) VALUES (?, ?, ?)',
          [userRoom, participantKey, data.optionIndex]
        );

        // Broadcast updated votes and participants to all participants
        broadcastToRoom(userRoom, {
          type: 'vote-update',
          votes: roomData.votes,
          participants: Array.from(roomData.participants.entries()).map(([key, value]) => ({
            name: key.split(':')[1],
            hasVoted: value.vote !== null
          }))
        });
        break;
    }
  });

  ws.on('close', () => {
    if (userRoom && participantKey) {  // Sử dụng participantKey để check
      const room = rooms.get(userRoom);
      if (room) {
        const participant = room.participants.get(participantKey);
        if (participant && participant.vote !== null) {
          room.votes[participant.vote]--;
        }
        room.participants.delete(participantKey);
        
        broadcastToRoom(userRoom, {
          type: 'participant-count',
          count: room.participants.size,
          participants: Array.from(room.participants.entries()).map(([key, value]) => ({
            name: key.split(':')[1],
            hasVoted: value.vote !== null
          }))
        });

        // Xóa room nếu không còn ai
        // if (room.participants.size === 0) {
        //   rooms.delete(userRoom);
        // }
      }
    }
  });
});

// Sửa hàm broadcastToRoom để gửi cập nhật danh sách người tham gia
function broadcastToRoom(roomId, message) {
  const room = rooms.get(roomId);
  if (!room) return;

  if (message.type === 'participant-count') {
    message.participants = Array.from(room.participants.entries()).map(([key, value]) => ({
      name: key.split(':')[1],
      hasVoted: value.vote !== null
    }));
  }

  room.participants.forEach(({ ws }) => {
    if (ws.readyState === WebSocket.OPEN) {
      ws.send(JSON.stringify(message));
    }
  });
}

const PORT = 3000;
server.listen(PORT, () => {
  console.log(`Server is running on port ${PORT}`);
});