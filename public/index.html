<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vote <PERSON></title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            color: #333;
        }

        .main-wrapper {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            overflow: hidden;
            min-height: calc(100vh - 40px);
        }

        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .content-wrapper {
            padding: 40px;
        }

        .container {
            display: none;
            animation: fadeIn 0.5s ease-in;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .active {
            display: block;
        }

        .form-group {
            margin-bottom: 25px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #555;
            font-size: 1.1em;
        }

        .form-group input[type="text"],
        .form-group input[type="number"] {
            width: 100%;
            padding: 15px;
            border: 2px solid #e1e5e9;
            border-radius: 10px;
            font-size: 16px;
            transition: all 0.3s ease;
            background: #f8f9fa;
        }

        .form-group input[type="text"]:focus,
        .form-group input[type="number"]:focus {
            outline: none;
            border-color: #4facfe;
            background: white;
            box-shadow: 0 0 0 3px rgba(79, 172, 254, 0.1);
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 1px;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.6);
        }

        .btn-secondary {
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            color: #333;
            box-shadow: 0 4px 15px rgba(252, 182, 159, 0.4);
        }

        .btn-secondary:hover {
            box-shadow: 0 8px 25px rgba(252, 182, 159, 0.6);
        }

        .option {
            margin: 15px 0;
            padding: 20px;
            border: 2px solid #e1e5e9;
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            background: white;
            position: relative;
            overflow: hidden;
        }

        .option::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(79, 172, 254, 0.1), transparent);
            transition: left 0.5s;
        }

        .option:hover::before {
            left: 100%;
        }

        .option:hover {
            border-color: #4facfe;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(79, 172, 254, 0.15);
        }

        .option.selected {
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            border-color: #2196f3;
            box-shadow: 0 8px 25px rgba(33, 150, 243, 0.3);
        }

        .option.disabled {
            background-color: #f9f9f9;
            color: #ccc;
            cursor: default;
            opacity: 0.6;
        }

        .stats {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 12px;
            margin-top: 30px;
            border: 1px solid #e9ecef;
        }

        .error {
            color: #dc3545;
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }

        #participantsList {
            margin: 15px 0;
            padding: 20px;
            border: 1px solid #e9ecef;
            border-radius: 12px;
            max-height: 200px;
            overflow-y: auto;
            background: white;
        }

        .participant {
            padding: 12px 15px;
            margin: 8px 0;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 8px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            transition: all 0.3s ease;
        }

        .participant:hover {
            transform: translateX(5px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .alert {
            padding: 20px;
            margin: 20px 0;
            background: linear-gradient(135deg, #d1e7dd 0%, #a3d9a4 100%);
            color: #0f5132;
            border: 1px solid #badbcc;
            border-radius: 12px;
            box-shadow: 0 4px 15px rgba(161, 217, 164, 0.3);
        }

        .final-results {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 30px;
            border-radius: 15px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .final-results h3 {
            margin-top: 0;
            color: #333;
            margin-bottom: 25px;
            font-size: 2em;
            text-align: center;
        }

        .result-item {
            margin: 20px 0;
            padding: 15px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
        }

        .result-text {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            font-weight: 600;
        }

        .result-count {
            color: #666;
            font-size: 0.95em;
            font-weight: 500;
        }

        .progress-bar {
            background: #e9ecef;
            height: 12px;
            border-radius: 6px;
            overflow: hidden;
            box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .progress {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            height: 100%;
            transition: width 0.8s ease;
            border-radius: 6px;
        }

        .total-info {
            margin-top: 25px;
            text-align: center;
            color: #666;
            font-size: 1.1em;
            font-weight: 600;
            padding: 15px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
        }

        .loading {
            padding: 20px;
            margin: 20px 0;
            background: linear-gradient(135deg, #cce5ff 0%, #b8daff 100%);
            color: #004085;
            border: 1px solid #b8daff;
            border-radius: 12px;
            text-align: center;
            font-weight: 600;
            box-shadow: 0 4px 15px rgba(184, 218, 255, 0.4);
        }

        .vote-status {
            font-size: 0.9em;
            font-weight: 600;
            padding: 4px 8px;
            border-radius: 15px;
        }

        .voted {
            color: #28a745;
            background: rgba(40, 167, 69, 0.1);
        }

        .not-voted {
            color: #dc3545;
            background: rgba(220, 53, 69, 0.1);
        }

        .room-info {
            margin-bottom: 30px;
            text-align: center;
        }

        .room-info h2 {
            color: #333;
            margin-bottom: 15px;
            font-size: 2.2em;
        }

        .room-id {
            color: #666;
            font-size: 1em;
            padding: 12px 20px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 25px;
            display: inline-block;
            margin-top: 10px;
            font-weight: 600;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .divider {
            margin: 40px 0;
            text-align: center;
            position: relative;
        }

        .divider::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, transparent, #ddd, transparent);
        }

        .divider span {
            background: white;
            padding: 0 20px;
            color: #666;
            font-weight: 600;
        }

        .card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
        }

        .card h2 {
            color: #333;
            margin-bottom: 25px;
            font-size: 2em;
            text-align: center;
        }

        .card h3 {
            color: #555;
            margin-bottom: 20px;
            font-size: 1.5em;
            text-align: center;
        }

        .option-input {
            width: 100%;
            margin: 8px 0 !important;
            padding: 12px 15px !important;
            border: 2px solid #e1e5e9 !important;
            border-radius: 8px !important;
            font-size: 16px !important;
            transition: all 0.3s ease !important;
            background: #f8f9fa !important;
        }

        .option-input:focus {
            outline: none !important;
            border-color: #4facfe !important;
            background: white !important;
            box-shadow: 0 0 0 3px rgba(79, 172, 254, 0.1) !important;
        }

        .btn-group {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin-top: 25px;
        }

        /* Join Room Simple Style */
        .join-simple {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            min-height: 60vh;
            text-align: center;
        }

        .join-form {
            background: white;
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            min-width: 350px;
            max-width: 400px;
        }

        .join-input {
            width: 100%;
            padding: 20px;
            margin: 10px 0;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 18px;
            text-align: center;
            background: #f8f9fa;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .join-input:focus {
            outline: none;
            border-color: #4facfe;
            background: white;
            box-shadow: 0 0 0 3px rgba(79, 172, 254, 0.1);
        }

        .join-input::placeholder {
            color: #999;
            font-weight: 500;
        }

        .join-btn {
            width: 100%;
            padding: 20px;
            margin-top: 15px;
            background: #333;
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 18px;
            font-weight: 700;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .join-btn:hover {
            background: #555;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
        }

        .create-link {
            margin-top: 30px;
        }

        .link-btn {
            background: none;
            border: none;
            color: #666;
            font-size: 16px;
            cursor: pointer;
            text-decoration: underline;
            transition: color 0.3s ease;
        }

        .link-btn:hover {
            color: #4facfe;
        }

        @media (max-width: 768px) {
            .main-wrapper {
                margin: 10px;
                min-height: calc(100vh - 20px);
            }

            .header {
                padding: 20px;
            }

            .header h1 {
                font-size: 2em;
            }

            .content-wrapper {
                padding: 20px;
            }

            .btn-group {
                flex-direction: column;
            }

            .btn {
                width: 100%;
            }

            .join-form {
                min-width: 280px;
                padding: 30px 20px;
            }

            .join-input {
                font-size: 16px;
                padding: 18px;
            }

            .join-btn {
                font-size: 16px;
                padding: 18px;
            }
        }
    </style>
</head>
<body>
    <div class="main-wrapper">
        <div class="header">
            <h1>Vote trực tuyến</h1>
        </div>

        <div class="content-wrapper">
            <div id="createRoom" class="container">
                <div class="card">
                    <h2>Tạo Phòng Bỏ Phiếu Mới</h2>
                    <div class="form-group">
                        <label for="question">Câu hỏi bỏ phiếu:</label>
                        <input type="text" id="question" placeholder="Nhập câu hỏi của bạn...">
                    </div>
                    <div class="form-group">
                        <label>Các lựa chọn:</label>
                        <div id="optionsList">
                            <input type="text" class="option-input" placeholder="Lựa chọn 1...">
                            <input type="text" class="option-input" placeholder="Lựa chọn 2...">
                        </div>
                        <button class="btn btn-secondary" onclick="addOption()">Thêm Lựa Chọn</button>
                    </div>
                    <div class="form-group">
                        <label for="endTime">Thời gian kết thúc (phút từ bây giờ):</label>
                        <input type="number" id="endTime" min="1" value="3" placeholder="3">
                    </div>
                    <div class="btn-group">
                        <button class="btn" onclick="createRoom()">Tạo Phòng</button>
                    </div>
                    <div class="error" id="createError"></div>

                    <div class="divider">
                        <span>HOẶC</span>
                    </div>

                    <h3>Tham Gia Phòng Có Sẵn</h3>
                    <div class="btn-group">
                        <button class="btn btn-secondary" onclick="showJoinRoom()">Tham Gia Phòng</button>
                    </div>
                </div>
            </div>

            <div id="joinRoom" class="container active">
                <div class="join-simple">
                    <div class="join-form">
                        <input type="text" id="roomId" placeholder="PIN" class="join-input">
                        <input type="text" id="userName" placeholder="Name" class="join-input">
                        <button class="join-btn" onclick="joinRoom()">ENTER</button>
                    </div>
                    <div class="error" id="joinError"></div>
                    <div class="create-link">
                        <button class="link-btn" onclick="showCreateRoom()">hoặc tạo Vote mới</button>
                    </div>
                </div>
            </div>

            <div id="votingRoom" class="container">
                <div class="room-info">
                    <h2 id="questionDisplay"></h2>
                    <div class="room-id">Mã Phòng: <span id="currentRoomId"></span></div>
                </div>
                <div id="optionsDisplay"></div>
                <div class="stats">
                    <p><strong>Người tham gia: <span id="participantCount">0</span></strong></p>
                    <div id="participantsList"></div>
                    <p id="timeRemaining"></p>
                </div>
            </div>
        </div>
    </div>

    <script>
        let ws;
        let selectedOption = null;
        let roomEndTime = null;
        let updateTimer;

        function showCreateRoom() {
            document.querySelectorAll('.container').forEach(c => c.classList.remove('active'));
            document.getElementById('createRoom').classList.add('active');
        }

        function showJoinRoom() {
            document.querySelectorAll('.container').forEach(c => c.classList.remove('active'));
            document.getElementById('joinRoom').classList.add('active');
        }

        function addOption() {
            const input = document.createElement('input');
            input.type = 'text';
            input.className = 'option-input';
            input.style = 'margin: 5px 0;';
            document.getElementById('optionsList').appendChild(input);
        }

        function createRoom() {
            const question = document.getElementById('question').value;
            const options = Array.from(document.getElementsByClassName('option-input'))
                .map(input => input.value)
                .filter(value => value.trim() !== '');
            const endTimeMinutes = parseInt(document.getElementById('endTime').value);

            if (!question || options.length < 2) {
                document.getElementById('createError').textContent =
                    'Vui lòng nhập câu hỏi và ít nhất 2 lựa chọn';
                return;
            }

            const startTime = new Date().toISOString();
            const endTime = new Date(Date.now() + endTimeMinutes * 60000).toISOString();

            fetch('/projects/live_vote_tracker/create-room', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ question, options, startTime, endTime })
            })
            .then(response => response.json())
            .then(data => {
                if (data.error) {
                    document.getElementById('createError').textContent = data.error;
                } else {
                    document.getElementById('roomId').value = data.roomId;
                    showJoinRoom();
                }
            })
            .catch(error => {
                document.getElementById('createError').textContent =
                    'Lỗi tạo phòng: ' + error.message;
            });
        }

        function joinRoom() {
            const roomId = document.getElementById('roomId').value;
            const userName = document.getElementById('userName').value;

            if (!roomId || !userName) {
                document.getElementById('joinError').textContent =
                    'Vui lòng nhập cả Mã Phòng và tên của bạn';
                return;
            }

            connectWebSocket(roomId, userName);
        }

        function connectWebSocket(roomId, userName) {
            // Tạo WebSocket URL
            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            const wsUrl = `${protocol}//${window.location.host}/projects/live_vote_tracker`;
            ws = new WebSocket(wsUrl);

            let isRoomEnded = false; // Add flag to track room state

            ws.onopen = () => {
                ws.send(JSON.stringify({
                    type: 'join',
                    roomId: roomId,
                    userName: userName
                }));
            };

            ws.onmessage = (event) => {
                const data = JSON.parse(event.data);

                switch (data.type) {
                    case 'room-state':
                        displayVotingRoom(data);
                        break;
                    case 'vote-update':
                        updateVoteCounts(data.votes);
                        if (data.participants) {
                            updateParticipantsList(data.participants);
                        }
                        break;
                    case 'participant-count':
                        document.getElementById('participantCount').textContent = data.count;
                        if (data.participants) {
                            updateParticipantsList(data.participants);
                        }
                        break;
                    case 'error':
                        document.getElementById('joinError').textContent = data.message;
                        return;
                    case 'room-ended':
                        isRoomEnded = true; // Set flag when room ends
                        displayFinalResults(data.roomId);
                        break;
                }

                document.querySelectorAll('.container').forEach(c => c.classList.remove('active'));
                document.getElementById('votingRoom').classList.add('active');
            };

            ws.onclose = () => {
                if (!isRoomEnded) { // Only show connection closed message if room hasn't ended
                    document.getElementById('joinError').textContent = 'Kết nối đã đóng';
                    showJoinRoom();
                }
            };
        }

        function displayVotingRoom(data) {
            document.getElementById('questionDisplay').textContent = data.question;
            document.getElementById('currentRoomId').textContent = data.roomId;
            const optionsDisplay = document.getElementById('optionsDisplay');
            optionsDisplay.innerHTML = '';

            data.options.forEach((option, index) => {
                const optionDiv = document.createElement('div');
                optionDiv.className = 'option';
                optionDiv.innerHTML = `
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <span style="font-weight: 600;">${option}</span>
                        <span style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); color: white; padding: 5px 12px; border-radius: 15px; font-size: 0.9em; font-weight: 600;">
                            <span class="vote-count">${data.votes[index]}</span>
                        </span>
                    </div>
                `;
                optionDiv.onclick = () => vote(index);
                optionsDisplay.appendChild(optionDiv);
            });

            updateVoteCounts(data.votes);
            document.getElementById('participantCount').textContent = data.participantCount;

            // Hiển thị danh sách người tham gia
            updateParticipantsList(data.participants);
        }

        // Cập nhật danh sách người tham gia
        function updateParticipantsList(participants) {
            const list = document.getElementById('participantsList');
            list.innerHTML = participants
                .map(p => `
                    <div class="participant">
                        <span style="font-weight: 600;">${p.name}</span>
                        <span class="vote-status ${p.hasVoted ? 'voted' : 'not-voted'}">
                            ${p.hasVoted ? 'Đã bỏ phiếu' : 'Chưa bỏ phiếu'}
                        </span>
                    </div>
                `)
                .join('');
        }

        function vote(optionIndex) {
            if (ws && ws.readyState === WebSocket.OPEN) {
                ws.send(JSON.stringify({
                    type: 'vote',
                    optionIndex: optionIndex
                }));

                document.querySelectorAll('.option').forEach((opt, index) => {
                    opt.classList.toggle('selected', index === optionIndex);
                });
                selectedOption = optionIndex;
            }
        }

        function updateVoteCounts(votes) {
            document.querySelectorAll('.vote-count').forEach((span, index) => {
                span.textContent = votes[index];
            });
        }

        // function disableVoting() {
        //     const options = document.querySelectorAll('.option');
        //     options.forEach(option => {
        //         option.onclick = null;
        //         option.style.cursor = 'default';
        //         option.classList.add('disabled');
        //     });
        // }

        // function showMessage(message) {
        //     const messageDiv = document.createElement('div');
        //     messageDiv.className = 'alert';
        //     messageDiv.textContent = message;
        //     document.getElementById('votingRoom').prepend(messageDiv);
        // }

        // Hiển thị kết quả cuối cùng khi phòng kết thúc
        async function displayFinalResults(roomId) {
            try {
                // Hiển thị trạng thái đang tải
                const loadingDiv = document.createElement('div');
                loadingDiv.className = 'loading';
                loadingDiv.textContent = 'Đang tải kết quả cuối cùng...';
                document.getElementById('votingRoom').prepend(loadingDiv);

                // Lấy kết quả từ API
                const response = await fetch(`/projects/live_vote_tracker/room-results/${roomId}`);
                if (!response.ok) {
                    throw new Error('Không thể tải kết quả');
                }

                const data = await response.json();

                // Xóa thông báo đang tải
                loadingDiv.remove();

                // Tạo container kết quả
                const resultsDiv = document.createElement('div');
                resultsDiv.className = 'final-results';

                // Thêm tiêu đề
                const header = document.createElement('h3');
                header.textContent = 'Kết Quả Cuối Cùng';
                resultsDiv.appendChild(header);

                // Hiển thị câu hỏi
                const questionDiv = document.createElement('div');
                questionDiv.className = 'final-question';
                questionDiv.innerHTML = `<h4 style="color: #555; margin-bottom: 20px; text-align: center;">${data.question}</h4>`;
                resultsDiv.appendChild(questionDiv);

                // Tính tổng số phiếu bầu
                const totalVotes = data.votes.reduce((sum, count) => sum + count, 0);

                // Tạo và sắp xếp mảng kết quả
                const results = data.options.map((option, index) => ({
                    text: option,
                    votes: data.votes[index]
                })).sort((a, b) => b.votes - a.votes);

                // Tạo danh sách kết quả
                const resultsList = document.createElement('div');
                resultsList.className = 'results-list';

                results.forEach((result, index) => {
                    const percentage = totalVotes > 0 ? (result.votes / totalVotes * 100).toFixed(1) : 0;
                    const resultItem = document.createElement('div');
                    resultItem.className = 'result-item';

                    resultItem.innerHTML = `
                        <div class="result-text">
                            <span>${result.text}</span>
                            <span class="result-count">
                                ${result.votes} (${percentage}%)
                            </span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress" style="width: ${percentage}%"></div>
                        </div>
                    `;
                    resultsList.appendChild(resultItem);
                });

                resultsDiv.appendChild(resultsList);

                // Thêm thông tin tổng số phiếu
                const totalInfo = document.createElement('div');
                totalInfo.className = 'total-info';
                totalInfo.innerHTML = `📊 <strong>Tổng số: ${totalVotes}</strong>`;
                resultsDiv.appendChild(totalInfo);

                // Thay thế nội dung hiện tại
                const votingRoom = document.getElementById('votingRoom');
                votingRoom.innerHTML = '';
                votingRoom.appendChild(resultsDiv);

                // Thêm nút quay lại
                const backButton = document.createElement('button');
                backButton.className = 'btn btn-secondary';
                backButton.innerHTML = 'Quay Lại Trang Chủ';
                backButton.onclick = () => {
                    showJoinRoom();
                    // Reset form
                    document.getElementById('roomId').value = '';
                    document.getElementById('userName').value = '';
                };
                votingRoom.appendChild(backButton);

            } catch (error) {
                console.error('Lỗi khi tải kết quả:', error);
                // Hiển thị thông báo lỗi cho người dùng
                const errorDiv = document.createElement('div');
                errorDiv.className = 'error';
                errorDiv.textContent = 'Không thể tải kết quả. Vui lòng thử lại sau.';
                document.getElementById('votingRoom').appendChild(errorDiv);
            }
        }
    </script>
</body>
</html>