<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Realtime Voting System</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .container {
            display: none;
        }
        .active {
            display: block;
        }
        .option {
            margin: 10px 0;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            cursor: pointer;
        }
        .option:hover {
            background-color: #f0f0f0;
        }
        .option.selected {
            background-color: #e3f2fd;
            border-color: #2196f3;
        }
        .option.disabled {
            background-color: #f9f9f9;
            color: #ccc;
            cursor: default;
        }
        .stats {
            color: #666;
            font-size: 0.9em;
            margin-top: 20px;
        }
        .error {
            color: red;
            margin: 10px 0;
        }
        #participantsList {
            margin: 10px 0;
            padding: 10px;
            border: 1px solid #eee;
            border-radius: 4px;
            max-height: 150px;
            overflow-y: auto;
        }
        .participant {
            padding: 3px 8px;
            margin: 2px 0;
            background: #f5f5f5;
            border-radius: 3px;
        }
        .alert {
            padding: 10px;
            margin: 10px 0;
            background-color: #d1e7dd;
            color: #0f5132;
            border: 1px solid #badbcc;
            border-radius: 4px;
        }
        .final-results {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .final-results h3 {
            margin-top: 0;
            color: #333;
            margin-bottom: 15px;
        }

        .result-item {
            margin: 10px 0;
        }

        .result-text {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
        }

        .result-count {
            color: #666;
            font-size: 0.9em;
        }

        .progress-bar {
            background: #e9ecef;
            height: 8px;
            border-radius: 4px;
            overflow: hidden;
        }

        .progress {
            background: #0d6efd;
            height: 100%;
            transition: width 0.6s ease;
        }

        .total-info {
            margin-top: 15px;
            text-align: right;
            color: #666;
            font-size: 0.9em;
        }

        .loading {
            padding: 10px;
            margin: 10px 0;
            background-color: #cce5ff;
            color: #004085;
            border: 1px solid #b8daff;
            border-radius: 4px;
            text-align: center;
        }
        .vote-status {
            float: right;
            font-size: 0.9em;
        }
        .voted {
            color: #28a745;
        }
        .not-voted {
            color: #dc3545;
        }
        .room-info {
            margin-bottom: 20px;
        }
        .room-id {
            color: #666;
            font-size: 0.9em;
            padding: 5px 10px;
            background: #f8f9fa;
            border-radius: 4px;
            display: inline-block;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div id="createRoom" class="container">
        <h2>Create a New Voting Room</h2>
        <div>
            <label for="question">Question:</label><br>
            <input type="text" id="question" style="width: 100%; margin: 5px 0;">
        </div>
        <div id="optionsContainer">
            <label>Options:</label><br>
            <div id="optionsList">
                <input type="text" class="option-input" style="margin: 5px 0;">
                <input type="text" class="option-input" style="margin: 5px 0;">
            </div>
            <button onclick="addOption()">Add Option</button>
        </div>
        <div style="margin: 10px 0;">
            <label for="endTime">End Time (minutes from now):</label><br>
            <input type="number" id="endTime" min="1" value="3">
        </div>
        <button onclick="createRoom()">Create Room</button>
        <div class="error" id="createError"></div>
        <hr>
        <h3>Or Join an Existing Room</h3>
        <button onclick="showJoinRoom()">Join Room</button>
    </div>

    <div id="joinRoom" class="container active">
        <h2>Join Voting Room</h2>
        <div>
            <label for="roomId">Room ID:</label><br>
            <input type="text" id="roomId">
        </div>
        <div>
            <label for="userName">Your Name:</label><br>
            <input type="text" id="userName">
        </div>
        <button onclick="joinRoom()">Join</button>
        <div class="error" id="joinError"></div>
        <hr>
        <h3>Or Create New Room</h3>
        <button onclick="showCreateRoom()">Create New Room</button>
    </div>

    <div id="votingRoom" class="container">
        <div class="room-info">
            <h2 id="questionDisplay"></h2>
            <div class="room-id">Room ID: <span id="currentRoomId"></span></div>
        </div>
        <div id="optionsDisplay"></div>
        <div class="stats">
            <p>Participants: <span id="participantCount">0</span></p>
            <div id="participantsList"></div>
            <p id="timeRemaining"></p>
        </div>
    </div>

    <script>
        let ws;
        let selectedOption = null;
        let roomEndTime = null;
        let updateTimer;

        function showCreateRoom() {
            document.querySelectorAll('.container').forEach(c => c.classList.remove('active'));
            document.getElementById('createRoom').classList.add('active');
        }

        function showJoinRoom() {
            document.querySelectorAll('.container').forEach(c => c.classList.remove('active'));
            document.getElementById('joinRoom').classList.add('active');
        }

        function addOption() {
            const input = document.createElement('input');
            input.type = 'text';
            input.className = 'option-input';
            input.style = 'margin: 5px 0;';
            document.getElementById('optionsList').appendChild(input);
        }

        function createRoom() {
            const question = document.getElementById('question').value;
            const options = Array.from(document.getElementsByClassName('option-input'))
                .map(input => input.value)
                .filter(value => value.trim() !== '');
            const endTimeMinutes = parseInt(document.getElementById('endTime').value);

            if (!question || options.length < 2) {
                document.getElementById('createError').textContent = 
                    'Please provide a question and at least 2 options';
                return;
            }

            const startTime = new Date().toISOString();
            const endTime = new Date(Date.now() + endTimeMinutes * 60000).toISOString();

            fetch('/projects/live_vote_tracker/create-room', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ question, options, startTime, endTime })
            })
            .then(response => response.json())
            .then(data => {
                if (data.error) {
                    document.getElementById('createError').textContent = data.error;
                } else {
                    document.getElementById('roomId').value = data.roomId;
                    showJoinRoom();
                }
            })
            .catch(error => {
                document.getElementById('createError').textContent = 
                    'Error creating room: ' + error.message;
            });
        }

        function joinRoom() {
            const roomId = document.getElementById('roomId').value;
            const userName = document.getElementById('userName').value;

            if (!roomId || !userName) {
                document.getElementById('joinError').textContent = 
                    'Please provide both Room ID and your name';
                return;
            }

            connectWebSocket(roomId, userName);
        }

        function connectWebSocket(roomId, userName) {
            // Tạo WebSocket URL
            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            const wsUrl = `${protocol}//${window.location.host}/projects/live_vote_tracker`;
            ws = new WebSocket(wsUrl);

            let isRoomEnded = false; // Add flag to track room state

            ws.onopen = () => {
                ws.send(JSON.stringify({
                    type: 'join',
                    roomId: roomId,
                    userName: userName
                }));
            };

            ws.onmessage = (event) => {
                const data = JSON.parse(event.data);

                switch (data.type) {
                    case 'room-state':
                        displayVotingRoom(data);
                        break;
                    case 'vote-update':
                        updateVoteCounts(data.votes);
                        if (data.participants) {
                            updateParticipantsList(data.participants);
                        }
                        break;
                    case 'participant-count':
                        document.getElementById('participantCount').textContent = data.count;
                        if (data.participants) {
                            updateParticipantsList(data.participants);
                        }
                        break;
                    case 'error':
                        document.getElementById('joinError').textContent = data.message;
                        return;
                    case 'room-ended':
                        isRoomEnded = true; // Set flag when room ends
                        displayFinalResults(data.roomId);
                        break;
                }

                document.querySelectorAll('.container').forEach(c => c.classList.remove('active'));
                document.getElementById('votingRoom').classList.add('active');
            };

            ws.onclose = () => {
                if (!isRoomEnded) { // Only show connection closed message if room hasn't ended
                    document.getElementById('joinError').textContent = 'Connection closed';
                    showJoinRoom();
                }
            };
        }

        function displayVotingRoom(data) {
            document.getElementById('questionDisplay').textContent = data.question;
            document.getElementById('currentRoomId').textContent = data.roomId;
            const optionsDisplay = document.getElementById('optionsDisplay');
            optionsDisplay.innerHTML = '';

            data.options.forEach((option, index) => {
                const optionDiv = document.createElement('div');
                optionDiv.className = 'option';
                optionDiv.innerHTML = `
                    ${option}
                    <div style="float: right">
                        Votes: <span class="vote-count">${data.votes[index]}</span>
                    </div>
                `;
                optionDiv.onclick = () => vote(index);
                optionsDisplay.appendChild(optionDiv);
            });

            updateVoteCounts(data.votes);
            document.getElementById('participantCount').textContent = data.participantCount;

            // Thêm hiển thị danh sách người tham gia
            updateParticipantsList(data.participants);
        }

        // Thêm hàm mới để cập nhật danh sách người tham gia
        function updateParticipantsList(participants) {
            const list = document.getElementById('participantsList');
            list.innerHTML = participants
                .map(p => `
                    <div class="participant">
                        ${p.name}
                        <span class="vote-status ${p.hasVoted ? 'voted' : 'not-voted'}">
                            ${p.hasVoted ? '✓ Voted' : 'Not voted'}
                        </span>
                    </div>
                `)
                .join('');
        }

        function vote(optionIndex) {
            if (ws && ws.readyState === WebSocket.OPEN) {
                ws.send(JSON.stringify({
                    type: 'vote',
                    optionIndex: optionIndex
                }));

                document.querySelectorAll('.option').forEach((opt, index) => {
                    opt.classList.toggle('selected', index === optionIndex);
                });
                selectedOption = optionIndex;
            }
        }

        function updateVoteCounts(votes) {
            document.querySelectorAll('.vote-count').forEach((span, index) => {
                span.textContent = votes[index];
            });
        }

        // function disableVoting() {
        //     const options = document.querySelectorAll('.option');
        //     options.forEach(option => {
        //         option.onclick = null;
        //         option.style.cursor = 'default';
        //         option.classList.add('disabled');
        //     });
        // }

        // function showMessage(message) {
        //     const messageDiv = document.createElement('div');
        //     messageDiv.className = 'alert';
        //     messageDiv.textContent = message;
        //     document.getElementById('votingRoom').prepend(messageDiv);
        // }

        // Add this function to handle room ended event
        async function displayFinalResults(roomId) {
            try {
                // Show loading state
                const loadingDiv = document.createElement('div');
                loadingDiv.className = 'loading';
                loadingDiv.textContent = 'Loading final results...';
                document.getElementById('votingRoom').prepend(loadingDiv);

                // Fetch results from API
                const response = await fetch(`/projects/live_vote_tracker/room-results/${roomId}`);
                if (!response.ok) {
                    throw new Error('Failed to fetch results');
                }
                
                const data = await response.json();
                
                // Remove loading message
                loadingDiv.remove();

                // Create results container
                const resultsDiv = document.createElement('div');
                resultsDiv.className = 'final-results';
                
                // Add header
                const header = document.createElement('h3');
                header.textContent = 'Final Results';
                resultsDiv.appendChild(header);

                // Question display
                const questionDiv = document.createElement('div');
                questionDiv.className = 'final-question';
                questionDiv.textContent = data.question;
                resultsDiv.appendChild(questionDiv);

                // Calculate total votes
                const totalVotes = data.votes.reduce((sum, count) => sum + count, 0);

                // Create and sort results array
                const results = data.options.map((option, index) => ({
                    text: option,
                    votes: data.votes[index]
                })).sort((a, b) => b.votes - a.votes);

                // Create results list
                const resultsList = document.createElement('div');
                resultsList.className = 'results-list';
                
                results.forEach(result => {
                    const percentage = totalVotes > 0 ? (result.votes / totalVotes * 100).toFixed(1) : 0;
                    const resultItem = document.createElement('div');
                    resultItem.className = 'result-item';
                    resultItem.innerHTML = `
                        <div class="result-text">
                            ${result.text}
                            <span class="result-count">
                                ${result.votes} votes (${percentage}%)
                            </span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress" style="width: ${percentage}%"></div>
                        </div>
                    `;
                    resultsList.appendChild(resultItem);
                });

                resultsDiv.appendChild(resultsList);

                // Add total votes info
                const totalInfo = document.createElement('div');
                totalInfo.className = 'total-info';
                totalInfo.textContent = `Total votes: ${totalVotes}`;
                resultsDiv.appendChild(totalInfo);

                // Replace existing content
                const votingRoom = document.getElementById('votingRoom');
                votingRoom.innerHTML = '';
                votingRoom.appendChild(resultsDiv);

            } catch (error) {
                console.error('Error fetching results:', error);
            }
        }
    </script>
</body>
</html>