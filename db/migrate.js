const sqlite3 = require('sqlite3').verbose();
const db = new sqlite3.Database('./voting.db');

db.serialize(() => {
  // Check if status column exists
  db.get("PRAGMA table_info(rooms)", (err, rows) => {
    if (err) {
      console.error('Error checking table schema:', err);
      return;
    }

    // Add status column if it doesn't exist
    db.run(`
      ALTER TABLE rooms 
      ADD COLUMN status TEXT DEFAULT 'active'
      `, (err) => {
        if (err) {
          if (err.message.includes('duplicate column name')) {
            console.log('Status column already exists');
          } else {
            console.error('Error adding status column:', err);
          }
        } else {
          console.log('Successfully added status column');
        }

        // Update existing rows to have 'active' status
        db.run(`
          UPDATE rooms 
          SET status = 'active' 
          WHERE status IS NULL
        `, (err) => {
          if (err) {
            console.error('Error updating existing rows:', err);
          } else {
            console.log('Successfully updated existing rows');
          }
          
          // Close the database connection
          db.close();
        });
    });
  });
});