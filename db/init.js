const sqlite3 = require('sqlite3').verbose();
const db = new sqlite3.Database('voting.db');

db.serialize(() => {
  // Create rooms table
  db.run(`CREATE TABLE IF NOT EXISTS rooms (
    room_id TEXT PRIMARY KEY,
    question TEXT,
    options TEXT,
    start_time DATETIME,
    end_time DATETIME
  )`);

  // Create votes table
  db.run(`CREATE TABLE IF NOT EXISTS votes (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    room_id TEXT,
    user_name TEXT,
    option_index INTEGER,
    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY(room_id) REFERENCES rooms(room_id)
  )`);
});

db.close();
