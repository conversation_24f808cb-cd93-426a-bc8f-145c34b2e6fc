version: '3'

services:
  live_vote_tracker:
    build:
      context: .
      dockerfile: Dockerfile
    image: live_vote_tracker
    container_name: live_vote_tracker
    volumes:
      - ./:/app
      - /app/node_modules
    environment:
      - NODE_ENV=production
    networks:
      - nginx_network # network
    restart: unless-stopped
networks:
  nginx_network:
    name: nginx_network
    driver: bridge
